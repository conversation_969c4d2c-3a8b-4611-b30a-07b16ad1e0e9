/**
 * Portfolio Website Styles
 * Advanced Color System with WCAG-Compliant Dark/Light Theme Support
 * 
 * This CSS uses CSS Custom Properties (CSS Variables) for dynamic theming
 * All colors are HSL-based for better manipulation and accessibility
 */

/* ===== CSS CUSTOM PROPERTIES (VARIABLES) ===== */
:root {
    /* Color System - HSL Values for Dynamic Theming */
    /* Primary Colors */
    --primary-hue: 220;
    --primary-saturation: 90%;
    --primary-lightness: 55%;
    
    /* Secondary Colors */
    --secondary-hue: 280;
    --secondary-saturation: 70%;
    --secondary-lightness: 60%;
    
    /* Success Color */
    --success-hue: 140;
    --success-saturation: 70%;
    --success-lightness: 50%;
    
    /* Warning Color */
    --warning-hue: 45;
    --warning-saturation: 90%;
    --warning-lightness: 55%;
    
    /* Error Color */
    --error-hue: 0;
    --error-saturation: 80%;
    --error-lightness: 55%;
    
    /* Neutral Colors */
    --neutral-hue: 220;
    --neutral-saturation: 10%;
    
    /* Typography */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Cascadia Code', Consolas, 'Courier New', monospace;
    
    /* Font Sizes - Fluid Typography */
    --font-size-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
    --font-size-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
    --font-size-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
    --font-size-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
    --font-size-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
    --font-size-2xl: clamp(1.5rem, 1.3rem + 1vw, 2rem);
    --font-size-3xl: clamp(2rem, 1.7rem + 1.5vw, 3rem);
    --font-size-4xl: clamp(2.5rem, 2rem + 2.5vw, 4rem);
    --font-size-5xl: clamp(3rem, 2.5rem + 2.5vw, 5rem);
    
    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;
    
    /* Line Heights */
    --line-height-tight: 1.25;
    --line-height-snug: 1.375;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
    --line-height-loose: 2;
    
    /* Spacing Scale */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;
    --space-4xl: 6rem;
    --space-5xl: 8rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
    
    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

/* ===== LIGHT THEME (DEFAULT) ===== */
:root {
    /* Background Colors - Light Mode */
    --bg-primary: hsl(var(--neutral-hue), var(--neutral-saturation), 98%);
    --bg-secondary: hsl(var(--neutral-hue), var(--neutral-saturation), 95%);
    --bg-tertiary: hsl(var(--neutral-hue), var(--neutral-saturation), 92%);
    --bg-elevated: hsl(var(--neutral-hue), var(--neutral-saturation), 100%);
    
    /* Text Colors - Light Mode */
    --text-primary: hsl(var(--neutral-hue), var(--neutral-saturation), 15%);
    --text-secondary: hsl(var(--neutral-hue), var(--neutral-saturation), 35%);
    --text-tertiary: hsl(var(--neutral-hue), var(--neutral-saturation), 55%);
    --text-inverse: hsl(var(--neutral-hue), var(--neutral-saturation), 95%);
    
    /* Border Colors - Light Mode */
    --border-primary: hsl(var(--neutral-hue), var(--neutral-saturation), 85%);
    --border-secondary: hsl(var(--neutral-hue), var(--neutral-saturation), 90%);
    --border-focus: hsl(var(--primary-hue), var(--primary-saturation), var(--primary-lightness));
    
    /* Brand Colors - Light Mode */
    --color-primary: hsl(var(--primary-hue), var(--primary-saturation), var(--primary-lightness));
    --color-primary-hover: hsl(var(--primary-hue), var(--primary-saturation), calc(var(--primary-lightness) - 5%));
    --color-primary-light: hsl(var(--primary-hue), var(--primary-saturation), 95%);
    
    --color-secondary: hsl(var(--secondary-hue), var(--secondary-saturation), var(--secondary-lightness));
    --color-secondary-hover: hsl(var(--secondary-hue), var(--secondary-saturation), calc(var(--secondary-lightness) - 5%));
    --color-secondary-light: hsl(var(--secondary-hue), var(--secondary-saturation), 95%);
    
    --color-success: hsl(var(--success-hue), var(--success-saturation), var(--success-lightness));
    --color-warning: hsl(var(--warning-hue), var(--warning-saturation), var(--warning-lightness));
    --color-error: hsl(var(--error-hue), var(--error-saturation), var(--error-lightness));
    
    /* Gradient Colors */
    --gradient-primary: linear-gradient(135deg, 
        hsl(var(--primary-hue), var(--primary-saturation), var(--primary-lightness)) 0%, 
        hsl(var(--secondary-hue), var(--secondary-saturation), var(--secondary-lightness)) 100%);
    --gradient-secondary: linear-gradient(135deg, 
        hsl(var(--secondary-hue), var(--secondary-saturation), var(--secondary-lightness)) 0%, 
        hsl(var(--primary-hue), var(--primary-saturation), calc(var(--primary-lightness) + 10%)) 100%);
}

/* ===== DARK THEME ===== */
[data-theme="dark"] {
    /* Background Colors - Dark Mode */
    --bg-primary: hsl(var(--neutral-hue), var(--neutral-saturation), 8%);
    --bg-secondary: hsl(var(--neutral-hue), var(--neutral-saturation), 12%);
    --bg-tertiary: hsl(var(--neutral-hue), var(--neutral-saturation), 16%);
    --bg-elevated: hsl(var(--neutral-hue), var(--neutral-saturation), 20%);
    
    /* Text Colors - Dark Mode */
    --text-primary: hsl(var(--neutral-hue), var(--neutral-saturation), 95%);
    --text-secondary: hsl(var(--neutral-hue), var(--neutral-saturation), 75%);
    --text-tertiary: hsl(var(--neutral-hue), var(--neutral-saturation), 55%);
    --text-inverse: hsl(var(--neutral-hue), var(--neutral-saturation), 15%);
    
    /* Border Colors - Dark Mode */
    --border-primary: hsl(var(--neutral-hue), var(--neutral-saturation), 25%);
    --border-secondary: hsl(var(--neutral-hue), var(--neutral-saturation), 20%);
    --border-focus: hsl(var(--primary-hue), var(--primary-saturation), calc(var(--primary-lightness) + 10%));
    
    /* Brand Colors - Dark Mode (Slightly brighter for better contrast) */
    --color-primary: hsl(var(--primary-hue), var(--primary-saturation), calc(var(--primary-lightness) + 10%));
    --color-primary-hover: hsl(var(--primary-hue), var(--primary-saturation), calc(var(--primary-lightness) + 15%));
    --color-primary-light: hsl(var(--primary-hue), var(--primary-saturation), 15%);
    
    --color-secondary: hsl(var(--secondary-hue), var(--secondary-saturation), calc(var(--secondary-lightness) + 10%));
    --color-secondary-hover: hsl(var(--secondary-hue), var(--secondary-saturation), calc(var(--secondary-lightness) + 15%));
    --color-secondary-light: hsl(var(--secondary-hue), var(--secondary-saturation), 15%);
    
    --color-success: hsl(var(--success-hue), var(--success-saturation), calc(var(--success-lightness) + 15%));
    --color-warning: hsl(var(--warning-hue), var(--warning-saturation), calc(var(--warning-lightness) + 10%));
    --color-error: hsl(var(--error-hue), var(--error-saturation), calc(var(--error-lightness) + 10%));
    
    /* Gradient Colors - Dark Mode */
    --gradient-primary: linear-gradient(135deg, 
        hsl(var(--primary-hue), var(--primary-saturation), calc(var(--primary-lightness) + 10%)) 0%, 
        hsl(var(--secondary-hue), var(--secondary-saturation), calc(var(--secondary-lightness) + 10%)) 100%);
    --gradient-secondary: linear-gradient(135deg, 
        hsl(var(--secondary-hue), var(--secondary-saturation), calc(var(--secondary-lightness) + 10%)) 0%, 
        hsl(var(--primary-hue), var(--primary-saturation), calc(var(--primary-lightness) + 20%)) 100%);
    
    /* Dark mode specific shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
}

/* ===== RESET AND BASE STYLES ===== */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
    line-height: 1.5;
}

body {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: color var(--transition-normal), background-color var(--transition-normal);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== ACCESSIBILITY ===== */
/* Focus styles for keyboard navigation */
*:focus {
    outline: 2px solid var(--border-focus);
    outline-offset: 2px;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border-primary: var(--text-primary);
        --border-secondary: var(--text-secondary);
    }
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    color: var(--text-primary);
    margin-bottom: var(--space-md);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--space-md);
    color: var(--text-secondary);
}

a {
    color: var(--color-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--color-primary-hover);
}

/* ===== LAYOUT UTILITIES ===== */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

@media (max-width: 768px) {
    .container {
        padding: 0 var(--space-md);
    }
}

/* ===== THEME TOGGLE BUTTON ===== */
.theme-toggle {
    position: fixed;
    top: var(--space-lg);
    right: var(--space-lg);
    z-index: var(--z-fixed);
    width: 3rem;
    height: 3rem;
    border: none;
    border-radius: var(--radius-full);
    background: var(--bg-elevated);
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
}

.theme-icon {
    width: 1.25rem;
    height: 1.25rem;
    transition: all var(--transition-normal);
}

.sun-icon {
    opacity: 1;
    transform: rotate(0deg);
}

.moon-icon {
    opacity: 0;
    transform: rotate(180deg);
    position: absolute;
}

[data-theme="dark"] .sun-icon {
    opacity: 0;
    transform: rotate(180deg);
}

[data-theme="dark"] .moon-icon {
    opacity: 1;
    transform: rotate(0deg);
}

@media (max-width: 768px) {
    .theme-toggle {
        top: var(--space-md);
        right: var(--space-md);
        width: 2.5rem;
        height: 2.5rem;
    }

    .theme-icon {
        width: 1rem;
        height: 1rem;
    }
}

/* ===== NAVIGATION ===== */
.nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-fixed);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-secondary);
    transition: all var(--transition-normal);
}

[data-theme="dark"] .nav {
    background: rgba(0, 0, 0, 0.8);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 4rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

.nav-logo {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-decoration: none;
    color: var(--text-primary);
    font-weight: var(--font-weight-bold);
    transition: transform var(--transition-fast);
}

.nav-logo:hover {
    transform: scale(1.05);
}

.logo-text {
    font-size: var(--font-size-xl);
    line-height: 1;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-subtitle {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    font-weight: var(--font-weight-medium);
    letter-spacing: 0.1em;
    text-transform: uppercase;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--space-lg);
    align-items: center;
}

.nav-link {
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--color-primary);
    background: var(--color-primary-light);
}

.nav-link-special {
    background: var(--gradient-primary);
    color: white;
    font-weight: var(--font-weight-semibold);
}

.nav-link-special:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--space-sm);
    gap: 4px;
}

.hamburger-line {
    width: 24px;
    height: 2px;
    background: var(--text-primary);
    transition: all var(--transition-normal);
    border-radius: 2px;
}

@media (max-width: 768px) {
    .nav-container {
        padding: 0 var(--space-md);
    }

    .nav-toggle {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        top: 4rem;
        left: 0;
        right: 0;
        background: var(--bg-elevated);
        flex-direction: column;
        padding: var(--space-lg);
        gap: var(--space-md);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
        border-bottom: 1px solid var(--border-secondary);
        box-shadow: var(--shadow-lg);
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-link {
        width: 100%;
        text-align: center;
        padding: var(--space-md);
    }
}

/* ===== HERO SECTION ===== */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    padding: var(--space-4xl) 0;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

.hero-svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.6;
}

.gradient-stop-1 {
    stop-color: var(--color-primary);
}

.gradient-stop-2 {
    stop-color: var(--color-secondary);
}

.gradient-stop-3 {
    stop-color: var(--color-primary);
}

.floating-shape {
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    animation-delay: 0s;
}

.shape-2 {
    animation-delay: 1s;
}

.shape-3 {
    animation-delay: 2s;
}

.shape-4 {
    animation-delay: 3s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(5deg);
    }
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-4xl);
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
    position: relative;
    z-index: 1;
}

.hero-text {
    animation: slideInLeft 1s ease-out;
}

.hero-title {
    margin-bottom: var(--space-lg);
}

.hero-greeting {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    margin-bottom: var(--space-sm);
}

.hero-name {
    display: block;
    font-size: var(--font-size-5xl);
    font-weight: var(--font-weight-black);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: var(--space-xs);
}

.hero-subtitle {
    display: block;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-medium);
    color: var(--text-tertiary);
    letter-spacing: 0.05em;
}

.hero-tagline {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--space-md);
    line-height: var(--line-height-relaxed);
}

.hero-motto {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--color-primary);
    margin-bottom: var(--space-2xl);
    font-style: italic;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== BUTTONS ===== */
.hero-buttons {
    display: flex;
    gap: var(--space-lg);
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-md) var(--space-xl);
    border: none;
    border-radius: var(--radius-lg);
    font-family: inherit;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-secondary {
    background: var(--bg-elevated);
    color: var(--text-primary);
    border: 2px solid var(--border-primary);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
    background: var(--color-primary-light);
    border-color: var(--color-primary);
    color: var(--color-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-icon {
    width: 1rem;
    height: 1rem;
    transition: transform var(--transition-fast);
}

.btn:hover .btn-icon {
    transform: translateX(2px);
}

/* ===== HERO VISUAL ===== */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    animation: slideInRight 1s ease-out;
}

.hero-avatar {
    position: relative;
}

.avatar-container {
    position: relative;
    width: 300px;
    height: 300px;
    border-radius: var(--radius-full);
    overflow: hidden;
}

.avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--radius-full);
    transition: transform var(--transition-slow);
}

.avatar-image:hover {
    transform: scale(1.05);
}

.avatar-ring {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border: 3px solid transparent;
    border-radius: var(--radius-full);
    background: var(--gradient-primary);
    background-clip: padding-box;
    animation: rotate 10s linear infinite;
}

.avatar-ring::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    right: 3px;
    bottom: 3px;
    background: var(--bg-primary);
    border-radius: var(--radius-full);
}

.avatar-glow {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    opacity: 0.3;
    filter: blur(20px);
    animation: pulse 3s ease-in-out infinite;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.05);
    }
}

/* ===== HERO SCROLL INDICATOR ===== */
.hero-scroll-indicator {
    position: absolute;
    bottom: var(--space-2xl);
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-sm);
    color: var(--text-tertiary);
    animation: bounce 2s infinite;
}

.scroll-mouse {
    width: 24px;
    height: 40px;
    border: 2px solid var(--text-tertiary);
    border-radius: 12px;
    position: relative;
}

.scroll-wheel {
    width: 4px;
    height: 8px;
    background: var(--text-tertiary);
    border-radius: 2px;
    position: absolute;
    top: 8px;
    left: 50%;
    transform: translateX(-50%);
    animation: scroll-wheel 2s infinite;
}

.scroll-text {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    letter-spacing: 0.1em;
    text-transform: uppercase;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

@keyframes scroll-wheel {
    0% {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateX(-50%) translateY(16px);
    }
}

/* ===== RESPONSIVE HERO ===== */
@media (max-width: 1024px) {
    .hero-content {
        gap: var(--space-2xl);
    }

    .avatar-container {
        width: 250px;
        height: 250px;
    }
}

@media (max-width: 768px) {
    .hero {
        padding: var(--space-2xl) 0;
        min-height: auto;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--space-2xl);
        text-align: center;
        padding: 0 var(--space-md);
    }

    .hero-visual {
        order: -1;
    }

    .avatar-container {
        width: 200px;
        height: 200px;
    }

    .hero-name {
        font-size: var(--font-size-4xl);
    }

    .hero-buttons {
        justify-content: center;
        gap: var(--space-md);
    }

    .btn {
        padding: var(--space-sm) var(--space-lg);
        font-size: var(--font-size-sm);
    }
}

/* ===== SECTIONS ===== */
section {
    padding: var(--space-5xl) 0;
    position: relative;
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-4xl);
}

.section-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-md);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== ABOUT SECTION ===== */
.about {
    background: var(--bg-secondary);
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
}

.about-description {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-relaxed);
    text-align: center;
    margin-bottom: var(--space-2xl);
}

.about-highlights {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-xl);
}

.highlight-item {
    display: flex;
    gap: var(--space-lg);
    padding: var(--space-xl);
    background: var(--bg-elevated);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.highlight-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.highlight-icon {
    flex-shrink: 0;
    width: 3rem;
    height: 3rem;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.highlight-icon svg {
    width: 1.5rem;
    height: 1.5rem;
}

.highlight-content h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-sm);
    color: var(--text-primary);
}

.highlight-content p {
    color: var(--text-secondary);
    margin: 0;
}

/* ===== SKILLS SECTION ===== */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-lg);
}

.skill-item {
    background: var(--bg-elevated);
    padding: var(--space-xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.skill-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform var(--transition-slow);
}

.skill-item:hover::before {
    transform: scaleX(1);
}

.skill-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.skill-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-lg);
}

.skill-name {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

.skill-level {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    background: var(--color-primary-light);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-md);
}

.skill-progress {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin-bottom: var(--space-md);
}

.skill-progress-bar {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    transition: width 1s ease-out;
    transform-origin: left;
}

.skill-category {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-weight: var(--font-weight-medium);
}
