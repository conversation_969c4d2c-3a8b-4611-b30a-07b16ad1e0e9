<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<PERSON><PERSON> (SK Raihan) - Electronics Engineering Student, Innovator, Maker, Developer & Content Creator. Founder of SKR Electronics Lab & SKR Projects Hub. Born to Build, Made to Innovate.">
    <meta name="keywords" content="<PERSON><PERSON>, SK Raihan, Electronics Engineering, Innovation, Maker, Developer, Content Creator, SKR Electronics Lab, SKR Projects Hub">
    <meta name="author" content="<PERSON><PERSON> (SK Raihan)">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://skrelectronicslab.com/">
    <meta property="og:title" content="Raihan (SK Raihan) - Born to Build, Made to Innovate">
    <meta property="og:description" content="Electronics Engineering Student, Innovator, Maker, Developer & Content Creator. Founder of SKR Electronics Lab & SKR Projects Hub.">
    <meta property="og:image" content="https://skrelectronicslab.com/images/profile.jpg">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://skrelectronicslab.com/">
    <meta property="twitter:title" content="Raihan (SK Raihan) - Born to Build, Made to Innovate">
    <meta property="twitter:description" content="Electronics Engineering Student, Innovator, Maker, Developer & Content Creator. Founder of SKR Electronics Lab & SKR Projects Hub.">
    <meta property="twitter:image" content="https://skrelectronicslab.com/images/profile.jpg">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    
    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/style.css">
    
    <title>Raihan (SK Raihan) - Born to Build, Made to Innovate</title>
</head>
<body>
    <!-- Theme Toggle Button -->
    <button id="theme-toggle" class="theme-toggle" aria-label="Toggle dark/light theme">
        <svg class="theme-icon sun-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="5"></circle>
            <line x1="12" y1="1" x2="12" y2="3"></line>
            <line x1="12" y1="21" x2="12" y2="23"></line>
            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
            <line x1="1" y1="12" x2="3" y2="12"></line>
            <line x1="21" y1="12" x2="23" y2="12"></line>
            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
        </svg>
        <svg class="theme-icon moon-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
        </svg>
    </button>

    <!-- Navigation -->
    <nav class="nav" id="nav">
        <div class="nav-container">
            <a href="#home" class="nav-logo">
                <span class="logo-text">Raihan</span>
                <span class="logo-subtitle">SKR</span>
            </a>
            <ul class="nav-menu" id="nav-menu">
                <li class="nav-item">
                    <a href="#home" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="#about" class="nav-link">About</a>
                </li>
                <li class="nav-item">
                    <a href="#skills" class="nav-link">Skills</a>
                </li>
                <li class="nav-item">
                    <a href="#projects" class="nav-link">Projects</a>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link">Contact</a>
                </li>
                <li class="nav-item">
                    <a href="links.html" class="nav-link nav-link-special">Links</a>
                </li>
            </ul>
            <button class="nav-toggle" id="nav-toggle" aria-label="Toggle navigation menu">
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </button>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section id="home" class="hero">
            <div class="hero-background">
                <svg class="hero-svg" viewBox="0 0 1200 800" preserveAspectRatio="xMidYMid slice">
                    <defs>
                        <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" class="gradient-stop-1"/>
                            <stop offset="50%" class="gradient-stop-2"/>
                            <stop offset="100%" class="gradient-stop-3"/>
                        </linearGradient>
                        <filter id="glow">
                            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                            <feMerge> 
                                <feMergeNode in="coloredBlur"/>
                                <feMergeNode in="SourceGraphic"/>
                            </feMerge>
                        </filter>
                    </defs>
                    <g class="hero-shapes">
                        <circle class="floating-shape shape-1" cx="200" cy="150" r="60" fill="url(#gradient1)" opacity="0.6"/>
                        <polygon class="floating-shape shape-2" points="800,100 850,50 900,100 850,150" fill="url(#gradient1)" opacity="0.4"/>
                        <rect class="floating-shape shape-3" x="1000" y="200" width="80" height="80" rx="20" fill="url(#gradient1)" opacity="0.5"/>
                        <path class="floating-shape shape-4" d="M300,600 Q350,550 400,600 T500,600" stroke="url(#gradient1)" stroke-width="3" fill="none" opacity="0.7"/>
                    </g>
                </svg>
                <div class="hero-particles" id="hero-particles"></div>
            </div>
            
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">
                        <span class="hero-greeting">Hello, I'm</span>
                        <span class="hero-name" id="hero-name">Raihan</span>
                        <span class="hero-subtitle">SK Raihan</span>
                    </h1>
                    <p class="hero-tagline" id="hero-tagline">
                        Electronics Engineering Student | Innovator | Maker | Developer | Content Creator
                    </p>
                    <p class="hero-motto" id="hero-motto">
                        Born to Build, Made to Innovate.
                    </p>
                    <div class="hero-buttons">
                        <a href="#projects" class="btn btn-primary">
                            <span>View My Work</span>
                            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M7 17L17 7M17 7H7M17 7V17"/>
                            </svg>
                        </a>
                        <a href="#contact" class="btn btn-secondary">
                            <span>Get In Touch</span>
                            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                <polyline points="22,6 12,13 2,6"/>
                            </svg>
                        </a>
                    </div>
                </div>
                <div class="hero-visual">
                    <div class="hero-avatar">
                        <div class="avatar-container">
                            <img src="images/profile.jpg" alt="Raihan (SK Raihan)" class="avatar-image" id="avatar-image">
                            <div class="avatar-ring"></div>
                            <div class="avatar-glow"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="hero-scroll-indicator">
                <div class="scroll-mouse">
                    <div class="scroll-wheel"></div>
                </div>
                <span class="scroll-text">Scroll to explore</span>
            </div>
        </section>

        <!-- About Section -->
        <section id="about" class="about">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">About Me</h2>
                    <p class="section-subtitle">Passionate about innovation and technology</p>
                </div>
                <div class="about-content">
                    <div class="about-text">
                        <p class="about-description" id="about-description">
                            I'm an Electronics Engineering student with a passion for innovation and making. 
                            As the founder of SKR Electronics Lab and SKR Projects Hub, I combine technical 
                            expertise with creative problem-solving to build solutions that matter.
                        </p>
                        <div class="about-highlights">
                            <div class="highlight-item">
                                <div class="highlight-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M9 12l2 2 4-4"/>
                                        <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"/>
                                        <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"/>
                                        <path d="M3 12c0 5.5 4.5 10 10 10s10-4.5 10-10"/>
                                    </svg>
                                </div>
                                <div class="highlight-content">
                                    <h3>Innovation Focused</h3>
                                    <p>Always pushing boundaries and exploring new possibilities</p>
                                </div>
                            </div>
                            <div class="highlight-item">
                                <div class="highlight-icon">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/>
                                    </svg>
                                </div>
                                <div class="highlight-content">
                                    <h3>Hands-On Maker</h3>
                                    <p>Building real solutions with electronics and code</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Skills Section -->
        <section id="skills" class="skills">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Skills & Expertise</h2>
                    <p class="section-subtitle">Technologies and tools I work with</p>
                </div>
                <div class="skills-grid" id="skills-grid">
                    <!-- Skills will be dynamically generated from config.js -->
                </div>
            </div>
        </section>

        <!-- Projects Section -->
        <section id="projects" class="projects">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Featured Projects</h2>
                    <p class="section-subtitle">Some of my recent work and innovations</p>
                </div>
                <div class="projects-grid" id="projects-grid">
                    <!-- Projects will be dynamically generated from config.js -->
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="contact">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Get In Touch</h2>
                    <p class="section-subtitle">Let's collaborate and build something amazing together</p>
                </div>
                <div class="contact-content">
                    <div class="contact-info">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                                    <polyline points="22,6 12,13 2,6"/>
                                </svg>
                            </div>
                            <div class="contact-details">
                                <h3>Email</h3>
                                <a href="mailto:<EMAIL>" id="contact-email"><EMAIL></a>
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                                    <polyline points="3.27,6.96 12,12.01 20.73,6.96"/>
                                    <line x1="12" y1="22.08" x2="12" y2="12"/>
                                </svg>
                            </div>
                            <div class="contact-details">
                                <h3>Website</h3>
                                <a href="https://www.skrelectronicslab.com" target="_blank" rel="noopener noreferrer">www.skrelectronicslab.com</a>
                            </div>
                        </div>
                    </div>
                    <form class="contact-form" id="contact-form">
                        <div class="form-group">
                            <label for="name">Name</label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="subject">Subject</label>
                            <input type="text" id="subject" name="subject" required>
                        </div>
                        <div class="form-group">
                            <label for="message">Message</label>
                            <textarea id="message" name="message" rows="5" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <span>Send Message</span>
                            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="22" y1="2" x2="11" y2="13"/>
                                <polygon points="22,2 15,22 11,13 2,9 22,2"/>
                            </svg>
                        </button>
                    </form>
                </div>
                <div class="social-links">
                    <a href="https://www.instagram.com/skr_electronics_lab" target="_blank" rel="noopener noreferrer" class="social-link" aria-label="Instagram">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="2" y="2" width="20" height="20" rx="5" ry="5"/>
                            <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"/>
                            <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"/>
                        </svg>
                    </a>
                    <a href="https://www.youtube.com/@skr_electronics_lab" target="_blank" rel="noopener noreferrer" class="social-link" aria-label="YouTube">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"/>
                            <polygon points="9.75,15.02 15.5,11.75 9.75,8.48"/>
                        </svg>
                    </a>
                    <a href="https://twitter.com/skrelectronics" target="_blank" rel="noopener noreferrer" class="social-link" aria-label="Twitter">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"/>
                        </svg>
                    </a>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-text">
                    <p>&copy; 2024 Raihan (SK Raihan). All rights reserved.</p>
                    <p>Born to Build, Made to Innovate.</p>
                </div>
                <div class="footer-links">
                    <a href="links.html">Links</a>
                    <a href="#home">Back to Top</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/config.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
