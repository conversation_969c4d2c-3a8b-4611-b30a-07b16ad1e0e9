/**
 * Portfolio Configuration File
 * 
 * This file contains all the customizable content for the portfolio website.
 * Edit the values below to personalize your portfolio without touching any other code.
 * 
 * IMPORTANT: Keep the structure intact. Only modify the values, not the property names.
 */

const portfolioConfig = {
    // Personal Information
    personal: {
        name: "<PERSON><PERSON>",
        fullName: "SK Raihan",
        title: "Electronics Engineering Student | Innovator | Maker | Developer | Content Creator",
        motto: "Born to Build, Made to Innovate.",
        bio: "I'm an Electronics Engineering student with a passion for innovation and making. As the founder of SKR Electronics Lab and SKR Projects Hub, I combine technical expertise with creative problem-solving to build solutions that matter.",
        
        // Profile image (place your image in the images/ folder)
        profileImage: "images/profile.jpg",
        
        // Contact Information
        email: "<EMAIL>",
        website: "https://www.skrelectronicslab.com",
        
        // Social Media Links
        social: {
            instagram: "https://www.instagram.com/skr_electronics_lab",
            youtube: "https://www.youtube.com/@skr_electronics_lab",
            twitter: "https://twitter.com/skrelectronics",
            github: "", // Add if you have GitHub
            linkedin: "", // Add if you have LinkedIn
        }
    },

    // Color Theme Configuration
    // These colors are used for both light and dark modes
    // HSL values allow for better color manipulation and accessibility
    colors: {
        // Primary brand colors
        primary: {
            hue: 220,        // Blue hue
            saturation: 90,  // High saturation for vibrancy
            lightness: 55    // Medium lightness
        },
        
        // Secondary accent colors
        secondary: {
            hue: 280,        // Purple hue
            saturation: 70,  // Medium-high saturation
            lightness: 60    // Medium lightness
        },
        
        // Success/positive color
        success: {
            hue: 140,        // Green hue
            saturation: 70,  // Medium-high saturation
            lightness: 50    // Medium lightness
        },
        
        // Warning color
        warning: {
            hue: 45,         // Orange hue
            saturation: 90,  // High saturation
            lightness: 55    // Medium lightness
        },
        
        // Error/danger color
        error: {
            hue: 0,          // Red hue
            saturation: 80,  // High saturation
            lightness: 55    // Medium lightness
        },
        
        // Neutral colors for backgrounds and text
        neutral: {
            hue: 220,        // Slight blue tint
            saturation: 10,  // Low saturation for neutrality
            lightness: 50    // Medium lightness (will be adjusted for light/dark modes)
        }
    },

    // Skills Configuration
    // Add, remove, or modify skills as needed
    // Set enabled: false to hide a skill without deleting it
    skills: [
        {
            name: "Electronics Design",
            level: 90,
            category: "Hardware",
            enabled: true,
            icon: "circuit" // Icon identifier for styling
        },
        {
            name: "Arduino/Microcontrollers",
            level: 85,
            category: "Hardware",
            enabled: true,
            icon: "microchip"
        },
        {
            name: "PCB Design",
            level: 80,
            category: "Hardware",
            enabled: true,
            icon: "cpu"
        },
        {
            name: "3D Printing & CAD",
            level: 75,
            category: "Design",
            enabled: true,
            icon: "cube"
        },
        {
            name: "Python Programming",
            level: 85,
            category: "Software",
            enabled: true,
            icon: "code"
        },
        {
            name: "Web Development",
            level: 80,
            category: "Software",
            enabled: true,
            icon: "globe"
        },
        {
            name: "IoT Development",
            level: 85,
            category: "Technology",
            enabled: true,
            icon: "wifi"
        },
        {
            name: "Content Creation",
            level: 90,
            category: "Creative",
            enabled: true,
            icon: "video"
        },
        {
            name: "Project Management",
            level: 80,
            category: "Management",
            enabled: true,
            icon: "target"
        }
    ],

    // Projects Configuration
    // Add your projects here. Images should be placed in the images/ folder
    projects: [
        {
            title: "Smart Home Automation System",
            description: "IoT-based home automation system with voice control, mobile app integration, and energy monitoring capabilities.",
            image: "images/project1.jpg",
            technologies: ["Arduino", "ESP32", "Python", "React", "Firebase"],
            category: "IoT",
            featured: true,
            links: {
                demo: "", // Add demo link if available
                github: "", // Add GitHub link if available
                video: "" // Add video link if available
            }
        },
        {
            title: "Wireless Power Transfer System",
            description: "Efficient wireless charging system for multiple devices using resonant inductive coupling technology.",
            image: "images/project2.jpg",
            technologies: ["Circuit Design", "PCB", "Power Electronics", "Simulation"],
            category: "Electronics",
            featured: true,
            links: {
                demo: "",
                github: "",
                video: ""
            }
        },
        {
            title: "AI-Powered Plant Monitoring",
            description: "Smart plant care system using computer vision and machine learning to monitor plant health and automate watering.",
            image: "images/project3.jpg",
            technologies: ["Raspberry Pi", "OpenCV", "TensorFlow", "Sensors", "Mobile App"],
            category: "AI/ML",
            featured: true,
            links: {
                demo: "",
                github: "",
                video: ""
            }
        },
        {
            title: "Custom PCB Calculator Tool",
            description: "Web-based tool for PCB design calculations including trace width, impedance, and component placement optimization.",
            image: "images/project4.jpg",
            technologies: ["JavaScript", "HTML5", "CSS3", "Web APIs"],
            category: "Software",
            featured: false,
            links: {
                demo: "",
                github: "",
                video: ""
            }
        },
        {
            title: "Educational Electronics Kit",
            description: "Comprehensive learning kit for electronics beginners with interactive tutorials and progressive projects.",
            image: "images/project5.jpg",
            technologies: ["Product Design", "Documentation", "Video Production"],
            category: "Education",
            featured: false,
            links: {
                demo: "",
                github: "",
                video: ""
            }
        }
    ],

    // Links Page Configuration
    // These links will appear on the dedicated links page
    links: [
        {
            title: "SKR Electronics Lab Website",
            description: "Main website with projects, tutorials, and resources",
            url: "https://www.skrelectronicslab.com",
            icon: "globe",
            featured: true
        },
        {
            title: "YouTube Channel",
            description: "Electronics tutorials, project builds, and tech reviews",
            url: "https://www.youtube.com/@skr_electronics_lab",
            icon: "youtube",
            featured: true
        },
        {
            title: "Instagram",
            description: "Behind-the-scenes content and project updates",
            url: "https://www.instagram.com/skr_electronics_lab",
            icon: "instagram",
            featured: true
        },
        {
            title: "Twitter/X",
            description: "Tech discussions and quick updates",
            url: "https://twitter.com/skrelectronics",
            icon: "twitter",
            featured: true
        },
        {
            title: "Email Contact",
            description: "Get in touch for collaborations and inquiries",
            url: "mailto:<EMAIL>",
            icon: "mail",
            featured: true
        }
    ],

    // Animation and Interaction Settings
    animations: {
        // Enable/disable various animation features
        enableParticles: true,
        enableCursorEffects: true,
        enableScrollAnimations: true,
        enableHoverEffects: true,
        enableSoundEffects: false, // Set to true to enable sound effects
        
        // Animation timing and easing
        transitionDuration: 300, // milliseconds
        scrollOffset: 100, // pixels from viewport to trigger scroll animations
        
        // Particle system settings
        particles: {
            count: 50,
            speed: 1,
            size: 2,
            opacity: 0.6
        }
    },

    // SEO and Meta Information
    seo: {
        siteName: "Raihan (SK Raihan) Portfolio",
        siteDescription: "Electronics Engineering Student, Innovator, Maker, Developer & Content Creator. Founder of SKR Electronics Lab & SKR Projects Hub. Born to Build, Made to Innovate.",
        keywords: ["Raihan", "SK Raihan", "Electronics Engineering", "Innovation", "Maker", "Developer", "Content Creator", "SKR Electronics Lab", "SKR Projects Hub"],
        author: "Raihan (SK Raihan)",
        language: "en",
        
        // Social media card settings
        ogImage: "https://skrelectronicslab.com/images/profile.jpg",
        twitterHandle: "@skrelectronics"
    }
};

// Export configuration for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = portfolioConfig;
}

// Make configuration globally available
window.portfolioConfig = portfolioConfig;
