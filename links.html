<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="All links to <PERSON><PERSON>'s (SK Raihan) social media, projects, and platforms. SKR Electronics Lab, YouTube, Instagram, Twitter, and more.">
    <meta name="keywords" content="Raihan, SK Raihan, Links, Social Media, SKR Electronics Lab, YouTube, Instagram, Twitter">
    <meta name="author" content="<PERSON><PERSON> (SK Raihan)">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://skrelectronicslab.com/links.html">
    <meta property="og:title" content="Raihan's Links - Connect with SKR Electronics Lab">
    <meta property="og:description" content="All links to Raihan's social media, projects, and platforms. Follow SKR Electronics Lab across all platforms.">
    <meta property="og:image" content="https://skrelectronicslab.com/images/profile.jpg">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://skrelectronicslab.com/links.html">
    <meta property="twitter:title" content="Raihan's Links - Connect with SKR Electronics Lab">
    <meta property="twitter:description" content="All links to Raihan's social media, projects, and platforms. Follow SKR Electronics Lab across all platforms.">
    <meta property="twitter:image" content="https://skrelectronicslab.com/images/profile.jpg">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    
    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/style.css">
    
    <title>Raihan's Links - Connect with SKR Electronics Lab</title>
</head>
<body class="links-page">
    <!-- Theme Toggle Button -->
    <button id="theme-toggle" class="theme-toggle" aria-label="Toggle dark/light theme">
        <svg class="theme-icon sun-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="5"></circle>
            <line x1="12" y1="1" x2="12" y2="3"></line>
            <line x1="12" y1="21" x2="12" y2="23"></line>
            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
            <line x1="1" y1="12" x2="3" y2="12"></line>
            <line x1="21" y1="12" x2="23" y2="12"></line>
            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
        </svg>
        <svg class="theme-icon moon-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
        </svg>
    </button>

    <!-- Back to Home Button -->
    <a href="index.html" class="back-home" aria-label="Back to home">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M19 12H5M12 19l-7-7 7-7"/>
        </svg>
        <span>Back to Portfolio</span>
    </a>

    <!-- Main Content -->
    <main class="links-main">
        <div class="links-container">
            <!-- Profile Section -->
            <div class="links-profile">
                <div class="links-avatar">
                    <img src="images/profile.jpg" alt="Raihan (SK Raihan)" class="links-avatar-image" id="links-avatar-image">
                    <div class="links-avatar-ring"></div>
                </div>
                <h1 class="links-name" id="links-name">Raihan</h1>
                <p class="links-subtitle" id="links-subtitle">SK Raihan</p>
                <p class="links-bio" id="links-bio">
                    Electronics Engineering Student | Innovator | Maker | Developer | Content Creator
                </p>
                <p class="links-motto" id="links-motto">
                    Born to Build, Made to Innovate.
                </p>
            </div>

            <!-- Links Grid -->
            <div class="links-grid" id="links-grid">
                <!-- Links will be dynamically generated from config.js -->
            </div>

            <!-- Footer -->
            <div class="links-footer">
                <p>&copy; 2024 Raihan (SK Raihan). All rights reserved.</p>
                <p>Connect with me across all platforms!</p>
            </div>
        </div>

        <!-- Background Animation -->
        <div class="links-background">
            <svg class="links-svg" viewBox="0 0 1200 800" preserveAspectRatio="xMidYMid slice">
                <defs>
                    <linearGradient id="linksGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" class="gradient-stop-1"/>
                        <stop offset="50%" class="gradient-stop-2"/>
                        <stop offset="100%" class="gradient-stop-3"/>
                    </linearGradient>
                </defs>
                <g class="links-shapes">
                    <circle class="floating-shape links-shape-1" cx="150" cy="200" r="40" fill="url(#linksGradient1)" opacity="0.3"/>
                    <polygon class="floating-shape links-shape-2" points="1000,150 1040,120 1080,150 1040,180" fill="url(#linksGradient1)" opacity="0.2"/>
                    <rect class="floating-shape links-shape-3" x="100" y="600" width="60" height="60" rx="15" fill="url(#linksGradient1)" opacity="0.25"/>
                    <circle class="floating-shape links-shape-4" cx="1100" cy="600" r="35" fill="url(#linksGradient1)" opacity="0.3"/>
                </g>
            </svg>
        </div>
    </main>

    <!-- Scripts -->
    <script src="js/config.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
